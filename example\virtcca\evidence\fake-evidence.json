{"report": {"tee": "virtcca", "parameter": {"x5c": "MIIEjjCCA3agAwIBAgIREU42SbPDXIIcRtuLWsjAufUwDQYJKoZIhvcNAQELBQAwPTELMAkGA1UEBhMCQ04xDzANBgNVBAoTBkh1YXdlaTEdMBsGA1UEAxMUSHVhd2VpIElUIFByb2R1Y3QgQ0EwHhcNMjQwNTI4MDIxNDE1WhcNMzkwNTI1MDIxNDE1WjA2MQswCQYDVQQGEwJDTjEPMA0GA1UEChMGSHVhd2VpMRYwFAYDVQQDEw0xMDIzQTc5ODkxMzhUMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEApmLUgbAe8uxQTAi0FNJPQZFW9j1wYZ/ezG2sdwmo+K3WZapOnJcAETnuIVEUdtRDg7UYF0L+W7/ZYtOVtXohvpv4FAoOxDAkifPTbcukRQ7NE3P0Gz39S5kkjcx0QR5LwM4AwWP/YaESGZkNwcln1HguiRCY7fj/07WVFGfAC+xwDT4rwmY23plxjfuJeKk4qgJf+0FbATmKoXQzC2/h+yK9qlrJzdQv02nEutIRufsA8b90Vbe7rVMUQy9IqOzxIG0WDNkoHcEo7sl3NugOeaSsMRiycSbax+onGeA/C2HTEmA6klQxbSPngIzvVMrGQsKRDDPaC5QoSAghIaAL1OvW/kBn76Mt6X8uTv8alwdOg1Dgjqdn01wpK+QT3tQN7oCP1g0tbqgQFFBsbpLhBw6dlmTNvIS7OPQJQ3eMgklo4vk0BH26r4Zn9c/1BkDnFtsnrWwSgUPX0FszAxzgzdraOxsbueNGIUs86rVAQ/0lnOVLP5ixSMrpSa5WymEgZ/pJXy02dXgv3LtYc9gGpQHl3kqoY0LnZLLhFSTu5AznJZLLkoNPTd8XGv+cgkBdqqTBWRNZ3jXHXHqwwAU9xwVdH6Z1hfOfFET437elytE8+jjN0ZCuIlb9usyzbaRo2qnMiHJHupMwfw52bvYCWPIyGUaRWgYpYLTkr2MF7A8CAwEAAaOBjzCBjDAfBgNVHSMEGDAWgBQSijfs+XNX1+SDurVvA+zdrhFOzzALBgNVHQ8EBAMCA/gwXAYIKwYBBQUHAQEEUDBOMCgGCCsGAQUFBzAChhxodHRwOi8vMTI3LjAuMC4xL2NhaXNzdWUuaHRtMCIGCCsGAQUFBzABhhZodHRwOi8vMTI3LjAuMC4xOjIwNDQzMA0GCSqGSIb3DQEBCwUAA4IBAQAsiyQZ7V9bOZDaFg9atpKWzV9B0gf1rtdTo2MNzFs1r63N2ZprUmQc649TJ3l5INcG0GHbdDqvvHpAVB41nlqXQPqAziHXp7v4YgQxXNnbDVt2mJIKcr2S6Ox9N59IqckSQouanmRmjbW7g90gsIS55rVH2QfuFgCpnEOf6MBEvWAU37omKFF6j56Cv/lMMNLsMzJgsE6dXwLEjDqykoB3ceantt/YSNZbltiF+zSlSBOdAD5T+uoquTP6cW4VIkmDJ8G9/pZaLJ4JycmwLtkuzjm3+c0vXPa88rOdpz3mO07BqsXYfSPXWMH208QxPwyQUbmyQwS5OSoeuRvGw22i"}, "tee_report": "2QGPoRms0VkFjdKERKEBOCSgWQN/WQN8pwpYQKhlpehdizbZ4DBRDgES2H0/nY15jtpWdQeB/q9o2qWyXhw3lWOl+8/Cwe4+ej8i0I3Tjp2ZBN30a7DucrsH6DYZrMtYQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAZrM1ZAiYwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQCmYtSBsB7y7FBMCLQU0k9BkVb2PXBhn97Mbax3Caj4rdZlqk6clwAROe4hURR21EODtRgXQv5bv9li05W1eiG+m/gUCg7EMCSJ89Nty6RFDs0Tc/QbPf1LmSSNzHRBHkvAzgDBY/9hoRIZmQ3ByWfUeC6JEJjt+P/TtZUUZ8AL7HANPivCZjbemXGN+4l4qTiqAl/7QVsBOYqhdDMLb+H7Ir2qWsnN1C/TacS60hG5+wDxv3RVt7utUxRDL0io7PEgbRYM2SgdwSjuyXc26A55pKwxGLJxJtrH6icZ4D8LYdMSYDqSVDFtI+eAjO9UysZCwpEMM9qLlChICCEhoAvU69b+QGfvoy3pfy5O/xqXB06DUOiOp2fTXCkr5BPe1A3ugI/WDS1uqBAUUGxukuEHDp2WZM28hLs49AlDd4yCSWji+TQEfbqvhmf1z/UGQOcW2yetbBKBQ9fQWzMDHODN2to7Gxu540YhSzzqtUBD/SWc5Us/mLFIyulJrlbKYSBn+klfLTZ1eC/cu1hz2AalAeXeSqhjQudksuEVJO7kDOclksuSg09N3xca/5yCQF2qpMFZE1neNcdcerDABT3HBV0fpnWF858URPjft6XK0Tz6OM3RkK4iVv26zLNtpGjaqcyIcke6kzB/DnZu9gJY8jIZRpFaBilgtOSvYwXsDwIDAQABGazMZ3NoYS0yNTYZrNBnc2hhLTI1Nhmszlgg6iHM4buDbKeXhKwRov4ojGceX2LCiDcY7Y2lmUeuKLEZrM+EWCA3pKBP5MshqAqBQZ6091kvcnEGlyf7XrhZ/RtQMZv65FggAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABYIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWCAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFkCAGKizQnneDJR1g9VifnkU7jSjx/k5CQdvvKnPNxpjcCSn6uKeRiNJ/KB/n5Df/znDt+v+ehU2Z+asxnirXXaVg0rbznrrQ0/k9+EoqyFlwug7NWZxkRUQaHyzxI1cG+GznmAjwQvW8zumNLuG2fIdGfpu69llOyuHtqzhZiwAbKJbreUC+NUNN6ErsAsr0qf+/8iabvSr3jU1UAvJflL4txImnNwOmROgmoDeL821epuGco1N2gyXozzu0qPPS/y+/Lpg6GX2plwvb9g1SKc/Icuq6ASd7ux2rl32S2tUMSQzTPeC8bMb53Dc2NanIJonJX7r8zRfWwzXOUzMZqNUiUAuLbCrYmBwCbVrjYavyyaqQetMO2h0Q1SIKOYbue4/xDyFBxlI3ISR/E5xpWNDoFdXiGsHgxkQEI8BTqB5qPBbgN2R5Yh+OSwJuAtXtDFqpiXB9+C6tZPbQKnn6LhPDAxkiTbIDGt2bYS00T3H8RqshOWfET11vp407PwlOhfXqg2mS08lo55xhpEx5syNeHTw5z32hOMv0mDbqRnF4LRMFOBdQJoabgcC/SnqHq9FyqPEIQ3lt4D6OT0nzzK5Yr/Cy0kjB6fSUUCies688vtvY6xg+rA1a+qE7lIX2LlnKLhsqJwnLU8jKcOouX4bDxHmvhchjdhvYD32xf5gzPgAA=="}}