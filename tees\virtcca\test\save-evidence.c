int main() {
     FILE *fp = fopen("report.hex", "rb");
    if (fp == NULL) {
        perror("Error opening file");
        return 1;
    }

    fseek(fp, 0, SEEK_END);
    size_t file_size = ftell(fp);
    rewind(fp);

    char *buffer = malloc(file_size + 1);
    if (buffer == NULL) {
        perror("Memory allocation error");
        return 1;
    }

    size_t bytes_read = fread(buffer, 1, file_size, fp);
    if (bytes_read != file_size) {
        perror("Error reading file");
        free(buffer);
        return 1;
    }

    char *binary_string = malloc(file_size / 2);
    if (binary_string == NULL) {
        perror("Memory allocation error");
        free(buffer);
        return 1;
    }

    for (size_t i = 0; i < file_size; i += 2) {
        unsigned int byte;
        sscanf(buffer + i, "%2x", &byte);
        binary_string[i / 2] = (char)byte;
    }


    FILE* fp1 = fopen("report.bin", "wb");
    if (fp == NULL) {
        printf("Error opening file.\n");
        return 1;
    }

    // 将 char* 数据写入文件
    fwrite(binary_string, 1, file_size / 2 , fp1);

    fclose(fp1);

    verify_full_token(binary_string, file_size / 2, "aik_cert.pem" );
    while (1)
        ;
}