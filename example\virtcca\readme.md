# quick start 

## 0.获取权限
### 角色

tcas有三个角色：
+ admin： 只负责创建和删除用户，系统内置，无法被创建。
+ policy：负责设置策略
+ secret：负责设置证书

policy和secret角色可同时分配给同一个人

### 创建用户
演示创建一个同时拥有policy和secret角色的用户

1）使用默认密码(admin/admin)获取admin-token
```json
curl --location --request POST 'http://127.0.0.1:8081/v1/user/login' \
--header 'Content-Type: application/json' \
--data-raw '{
    "passWord": "YWRtaW4=",
    "userName": "YWRtaW4="
}'
```
response:

```json
{
    "code": 200,
    "message": "login success",
    "token": "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "userID": "user-06e8735b-f419-4249-8c7a-fa5bae48cfcd"
}
```

2)以返回的token作为参数，创建用户
```json
curl --location --request POST 'http://127.0.0.1:8081/v1/user/create' \
--header 'Authorization: *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' \
--header 'Content-Type: application/json' \
--data-raw '{
    "userName":"test",
    "passWord":"dGVzdEAxMjM=",
    "display":"测试用户",
    "roles":"secret,policy"
}'
```
其中用户名/密码：test/test@123, passWord需要进行base64编码，即：base64(test@123)

3）用test用户登录，获取test用户的token
```json
curl --location --request POST 'http://127.0.0.1:8081/v1/user/login' \
--header 'Content-Type: application/json' \
--data-raw '{
    "userName": "dGVzdA==",
    "passWord": "dGVzdEAxMjM="
}'
```

response:
```json
{
    "code": 200,
    "message": "login success",
    "token": "<login token>",
    "userID": "user-91e52f0b-3381-43e4-8ab6-2fec37ae4796"
}
```
其中test's token 用于设置策略和秘密时鉴权

4) 获取test用户的apikey
```
curl --location --request GET 'http://127.0.0.1:8081/v1/user/info' \
--header 'Content-Type: application/json' \
--header 'Authorization: <login token>' \
--data-raw '""'

```

response:
```json
{
    "code": 200,
    "message": "get user info success",
    "User": {
        "No": 5,
        "userId": "user-91e52f0b-3381-43e4-8ab6-2fec37ae4796",
        "Display": "测试用户",
        "userName": "test",
        "status": "first",
        "roles": "secret,policy",
        "apiKey": "IjcZBHN1ccjUojTTQY534Po92EKDYDC9fkdteMqJAkhUQurv",
        "lastLogin": "2024-09-11T01:47:34.266134114Z",
        "createTime": "2024-09-11T01:47:34.266136964Z",
        "updateTime": "2024-09-11T01:47:34.266138204Z"
    }
}
```
其中apiKey用户调用apiKey用户调用Attest相关接口时鉴权

5） 更新apiKey
apiKey可更新，更新后，旧的key失效
```shell
curl --location --request PUT 'http://127.0.0.1:8081/v1/user/update/apikey' \
--header 'Content-Type: application/json' \
--header 'Authorization: <login token> ' 
```

## 1. 设置策略

根据需要修改`policy/trustnode.rego` 策略文件
在allow_node中配置相关的条件,如下的字段均可配置，给是如下node.tee.<字段> == <条件值>
```shell
        "virtcca_challenge": "0000",
        "virtcca_hash_algo_id": "sha-256",
        "virtcca_pub_key": "2222222",
        "virtcca_pub_key_hash_algo_id": "sha-256",
        "virtcca_rem0": "37a4a04fe4cb21a80a81419eb4f7592f7271069727fb5eb859fd1b50319bfae4",
        "virtcca_rem1": "0000000000000000000000000000000000000000000000000000000000000000",
        "virtcca_rem2": "0000000000000000000000000000000000000000000000000000000000000000",
        "virtcca_rem3": "0000000000000000000000000000000000000000000000000000000000000000",
        "virtcca_rim": "ea21cce1bb836ca79784ac11a2fe288c671e5f62c2883718ed8da59947ae28b1",
        "virtcca_rpv": "0000000000000000000000000000000000000000000000000000000000000000000000"
      }
```

base64编码策略文件
```shell
cat policy/trustnode.rego |base64 -w 0
```

将获取的base64值作为policy_rego参数调用tcas的policy接口，设置policy
```shell
curl --location --request POST 'http://127.0.0.1:8081/v1/policy' --header 'Authorization: <login token>' --header 'Content-Type: application/json' --data-raw '{
    "policy_rego":"cGFja2FnZSB0cnVzdG5vZGUKCmltcG9ydCBmdXR1cmUua2V5d29yZHMuZXZlcnkKCmRlZmF1bHQgdHJ1c3Rfbm9kZSA6PSBmYWxzZQoKdHJ1c3Rfbm9kZSB7CglhbGxvd2VkX25vZGUoaW5wdXQudHJ1c3Rfbm9kZSkKfQoKYWxsb3dlZF9ub2RlKG5vZGUpIHsKCW5vZGUudGVlLnZpcnRjY2FfcmltID09ICJlYTIxY2NlMWJiODM2Y2E3OTc4NGFjMTFhMmZlMjg4YzY3MWU1ZjYyYzI4ODM3MThlZDhkYTU5OTQ3YWUyOGIxIgoJbm9kZS50ZWUudmlydGNjYV9yZW0wID0gIjM3YTRhMDRmZTRjYjIxYTgwYTgxNDE5ZWI0Zjc1OTJmNzI3MTA2OTcyN2ZiNWViODU5ZmQxYjUwMzE5YmZhZTQiCn0K",
    "policy_name":"test-vcca",
    "attestation_type":"trust_node"
}'

```
调用成功，返回
```shell
{"code":200,"message":"add policy successful","policy_id":"7cf745a6-e37c-4bfa-a0b2-b99c646b663f"}r
```
>注意："attestation_type":"trust_node" 是固定的，"policy_name"可自定义

## 2. Attest 
attest 有3种模式：

1.当没有设置策略时，只校验report的合法性，若合法则返回token

2.当设置了策略，但未在attest参数中指定policy_ids,则会选择最新的policy来进行校验

3.如果在attest参数中设置了nonce参数，则必须在report中嵌入nonce，才能过校验

2.1 不添加policy和nonce

```shell
curl --location --request POST 'http://127.0.0.1:8081/v1/attest' --header 'API-KEY: <apikey>'  --header 'Content-Type: application/json' --data-raw '{
  "report": {
    "tee": "virtcca",
    "parameter": {
      "x5c": "MIIEjjCCA3agAwIBAgIREU42SbPDXIIcRtuLWsjAufUwDQYJKoZIhvcNAQELBQAwPTELMAkGA1UEBhMCQ04xDzANBgNVBAoTBkh1YXdlaTEdMBsGA1UEAxMUSHVhd2VpIElUIFByb2R1Y3QgQ0EwHhcNMjQwNTI4MDIxNDE1WhcNMzkwNTI1MDIxNDE1WjA2MQswCQYDVQQGEwJDTjEPMA0GA1UEChMGSHVhd2VpMRYwFAYDVQQDEw0xMDIzQTc5ODkxMzhUMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEApmLUgbAe8uxQTAi0FNJPQZFW9j1wYZ/ezG2sdwmo+K3WZapOnJcAETnuIVEUdtRDg7UYF0L+W7/ZYtOVtXohvpv4FAoOxDAkifPTbcukRQ7NE3P0Gz39S5kkjcx0QR5LwM4AwWP/YaESGZkNwcln1HguiRCY7fj/07WVFGfAC+xwDT4rwmY23plxjfuJeKk4qgJf+0FbATmKoXQzC2/h+yK9qlrJzdQv02nEutIRufsA8b90Vbe7rVMUQy9IqOzxIG0WDNkoHcEo7sl3NugOeaSsMRiycSbax+onGeA/C2HTEmA6klQxbSPngIzvVMrGQsKRDDPaC5QoSAghIaAL1OvW/kBn76Mt6X8uTv8alwdOg1Dgjqdn01wpK+QT3tQN7oCP1g0tbqgQFFBsbpLhBw6dlmTNvIS7OPQJQ3eMgklo4vk0BH26r4Zn9c/1BkDnFtsnrWwSgUPX0FszAxzgzdraOxsbueNGIUs86rVAQ/0lnOVLP5ixSMrpSa5WymEgZ/pJXy02dXgv3LtYc9gGpQHl3kqoY0LnZLLhFSTu5AznJZLLkoNPTd8XGv+cgkBdqqTBWRNZ3jXHXHqwwAU9xwVdH6Z1hfOfFET437elytE8+jjN0ZCuIlb9usyzbaRo2qnMiHJHupMwfw52bvYCWPIyGUaRWgYpYLTkr2MF7A8CAwEAAaOBjzCBjDAfBgNVHSMEGDAWgBQSijfs+XNX1+SDurVvA+zdrhFOzzALBgNVHQ8EBAMCA/gwXAYIKwYBBQUHAQEEUDBOMCgGCCsGAQUFBzAChhxodHRwOi8vMTI3LjAuMC4xL2NhaXNzdWUuaHRtMCIGCCsGAQUFBzABhhZodHRwOi8vMTI3LjAuMC4xOjIwNDQzMA0GCSqGSIb3DQEBCwUAA4IBAQAsiyQZ7V9bOZDaFg9atpKWzV9B0gf1rtdTo2MNzFs1r63N2ZprUmQc649TJ3l5INcG0GHbdDqvvHpAVB41nlqXQPqAziHXp7v4YgQxXNnbDVt2mJIKcr2S6Ox9N59IqckSQouanmRmjbW7g90gsIS55rVH2QfuFgCpnEOf6MBEvWAU37omKFF6j56Cv/lMMNLsMzJgsE6dXwLEjDqykoB3ceantt/YSNZbltiF+zSlSBOdAD5T+uoquTP6cW4VIkmDJ8G9/pZaLJ4JycmwLtkuzjm3+c0vXPa88rOdpz3mO07BqsXYfSPXWMH208QxPwyQUbmyQwS5OSoeuRvGw22i"
    },
    "tee_report": "2QGPoRms0VkFjdKERKEBOCSgWQN/WQN8pwpYQB+rVhlgPiYD0ZH5b9+p4XJyUA/3qrpkEnfnNkdJIaQxThkGQ2hlWTR7ITHLvl/rOiBjQND1JYDf6HKTZw/Jv0MZrMtYQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAZrM1ZAiYwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQCmYtSBsB7y7FBMCLQU0k9BkVb2PXBhn97Mbax3Caj4rdZlqk6clwAROe4hURR21EODtRgXQv5bv9li05W1eiG+m/gUCg7EMCSJ89Nty6RFDs0Tc/QbPf1LmSSNzHRBHkvAzgDBY/9hoRIZmQ3ByWfUeC6JEJjt+P/TtZUUZ8AL7HANPivCZjbemXGN+4l4qTiqAl/7QVsBOYqhdDMLb+H7Ir2qWsnN1C/TacS60hG5+wDxv3RVt7utUxRDL0io7PEgbRYM2SgdwSjuyXc26A55pKwxGLJxJtrH6icZ4D8LYdMSYDqSVDFtI+eAjO9UysZCwpEMM9oLlChICCEhoAvU69b+QGfvoy3pfy5O/xqXB06DUOCOp2fTXCkr5BPe1A3ugI/WDS1uqBAUUGxukuEHDp2WZM28hLs49AlDd4yCSWji+TQEfbqvhmf1z/UGQOcW2yetbBKBQ9fQWzMDHODN2to7Gxu540YhSzzqtUBD/SWc5Us/mLFIyulJrlbKYSBn+klfLTZ1eC/cu1hz2AalAeXeSqhjQudksuEVJO7kDOclksuSg09N3xca/5yCQF2qpMFZE1neNcdcerDABT3HBV0fpnWF858URPjft6XK0Tz6OM3RkK4iVv26zLNtpGjaqcyIcke6kzB/DnZu9gJY8jIZRpFaBilgtOSvYwXsDwIDAQABGazMZ3NoYS0yNTYZrNBnc2hhLTI1Nhmszlgg6iHM4buDbKeXhKwRov4ojGceX2LCiDcY7Y2lmUeuKLEZrM+EWCA3pKBP5MshqAqBQZ6091kvcnEGlyf7XrhZ/RtQMZv65FggAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABYIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWCAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFkCAE3UOgzQgssusjyzjdyaOElJKlqkhAfPQvO8CnD2fRuEEBhQzktkX6ad5rCwzlI3LrCmog7JFiGyexVw0ZWZkWttsF9wiGiE87AAnB3Bu7CduuE257ZvHhXNXMh6odgsS2SXDkB531ReomZp7lN0Rwg+LfYVBsVT5vSSCi8ixduL6DqH3CdEr9d30cDw5BiDYF0rj13q87U/+euosu3kmF0ROsMsOHkugEk+ztEvbfjKqEpKNRUvMsw52ntMRv+Zqe7Ib6DWYdiYAHHufrhAXPIX5oF8TNrZ5iK3Dq62Gmb0mbvd4FQ8l7RuCoJF/+SIM54lTAF/o30ww3Fch6m5zejK8Qyx0KDjAN+6C60j4Z0sAP3qINwMq5I5jYo0Gxf6wYItcpXeHE26xtoWatbW043ZryE6js/qwuoXmNc1xE2b3NACg6P/YB4uFsK2bmIqear0/uNkOO5Fxs2bi754hM2T/vWOuyvgma0IWdmRkrWt/kYz+vwMsx2vnSxopOivTn6yAEwWrSSa9X7fV3k+lcVrfCjCeDrr5MrSbd+GJp5GKbG74oSdY0xlJXJEZ7HM/UBOJ0A0K8VeShdyhK7zj2H6WMmm14UDUnMqK2iScjIO+NYD8ycZG7gzKRWObCbRgMj7w6eBpbWDXHwvaOB9DHapQN7PHqk5+jTFDH9JQo5b"
  },
  "policy_ids":[],
  "nonce":""
}'
```

+ `tee`: 类型，必须为"virtcca"
+ `parameter`: 认证需要的额外参数，virtcca 必须传递x5c格式的aik证书
+ `tee_report`: 传递base64编码的report

若校验成功则返回token
```json
{
  "code": 200,
  "message": "attest successful",
  "token": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************_J0nXQWCZ7JpVi12nu5pyTM8KE0eRjz4f3B_9zzqaKbRKqXwzltMaF3h-uDXRk1w"
}
```

2.2 添加nonce
获取nonce
```shell
curl --location --request GET 'http://127.0.0.1:8081/v1/nonce'
```
正确返回
```shell
{
  "code": 200,
  "message": "get nonce successful",
  "data": {
    "nonce": "81a22025655341608f09656bcc30f1e9",
    "expired": "2024-07-23T14:17:11.680812246+08:00"
  }
}
```
nonce的有效期为3分钟，必须在3分钟中内将nonce嵌入到report，并完成attest。嵌入方式如下
```shell
challenge=hex(sha256(nonce))

#golang example
challenge := fmt.Sprintf("%x", sha256.Sum256([]byte(nonce)))
```

生成报告后，调用attest接口
```shell
curl --location --request POST 'http://127.0.0.1:8081/v1/attest' --header 'Content-Type: application/json' --header 'API-KEY: <test's apikey>' --data-raw '{
  "report": {
    "tee": "virtcca",
    "parameter": {
      "x5c": "MIIEjjCCA3agAwIBAgIREU42SbPDXIIcRtuLWsjAufUwDQYJKoZIhvcNAQELBQAwPTELMAkGA1UEBhMCQ04xDzANBgNVBAoTBkh1YXdlaTEdMBsGA1UEAxMUSHVhd2VpIElUIFByb2R1Y3QgQ0EwHhcNMjQwNTI4MDIxNDE1WhcNMzkwNTI1MDIxNDE1WjA2MQswCQYDVQQGEwJDTjEPMA0GA1UEChMGSHVhd2VpMRYwFAYDVQQDEw0xMDIzQTc5ODkxMzhUMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEApmLUgbAe8uxQTAi0FNJPQZFW9j1wYZ/ezG2sdwmo+K3WZapOnJcAETnuIVEUdtRDg7UYF0L+W7/ZYtOVtXohvpv4FAoOxDAkifPTbcukRQ7NE3P0Gz39S5kkjcx0QR5LwM4AwWP/YaESGZkNwcln1HguiRCY7fj/07WVFGfAC+xwDT4rwmY23plxjfuJeKk4qgJf+0FbATmKoXQzC2/h+yK9qlrJzdQv02nEutIRufsA8b90Vbe7rVMUQy9IqOzxIG0WDNkoHcEo7sl3NugOeaSsMRiycSbax+onGeA/C2HTEmA6klQxbSPngIzvVMrGQsKRDDPaC5QoSAghIaAL1OvW/kBn76Mt6X8uTv8alwdOg1Dgjqdn01wpK+QT3tQN7oCP1g0tbqgQFFBsbpLhBw6dlmTNvIS7OPQJQ3eMgklo4vk0BH26r4Zn9c/1BkDnFtsnrWwSgUPX0FszAxzgzdraOxsbueNGIUs86rVAQ/0lnOVLP5ixSMrpSa5WymEgZ/pJXy02dXgv3LtYc9gGpQHl3kqoY0LnZLLhFSTu5AznJZLLkoNPTd8XGv+cgkBdqqTBWRNZ3jXHXHqwwAU9xwVdH6Z1hfOfFET437elytE8+jjN0ZCuIlb9usyzbaRo2qnMiHJHupMwfw52bvYCWPIyGUaRWgYpYLTkr2MF7A8CAwEAAaOBjzCBjDAfBgNVHSMEGDAWgBQSijfs+XNX1+SDurVvA+zdrhFOzzALBgNVHQ8EBAMCA/gwXAYIKwYBBQUHAQEEUDBOMCgGCCsGAQUFBzAChhxodHRwOi8vMTI3LjAuMC4xL2NhaXNzdWUuaHRtMCIGCCsGAQUFBzABhhZodHRwOi8vMTI3LjAuMC4xOjIwNDQzMA0GCSqGSIb3DQEBCwUAA4IBAQAsiyQZ7V9bOZDaFg9atpKWzV9B0gf1rtdTo2MNzFs1r63N2ZprUmQc649TJ3l5INcG0GHbdDqvvHpAVB41nlqXQPqAziHXp7v4YgQxXNnbDVt2mJIKcr2S6Ox9N59IqckSQouanmRmjbW7g90gsIS55rVH2QfuFgCpnEOf6MBEvWAU37omKFF6j56Cv/lMMNLsMzJgsE6dXwLEjDqykoB3ceantt/YSNZbltiF+zSlSBOdAD5T+uoquTP6cW4VIkmDJ8G9/pZaLJ4JycmwLtkuzjm3+c0vXPa88rOdpz3mO07BqsXYfSPXWMH208QxPwyQUbmyQwS5OSoeuRvGw22i"
    },
    "tee_report": "2QGPoRms0VkFjdKERKEBOCSgWQN/WQN8pwpYQB+rVhlgPiYD0ZH5b9+p4XJyUA/3qrpkEnfnNkdJIaQxThkGQ2hlWTR7ITHLvl/rOiBjQND1JYDf6HKTZw/Jv0MZrMtYQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAZrM1ZAiYwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQCmYtSBsB7y7FBMCLQU0k9BkVb2PXBhn97Mbax3Caj4rdZlqk6clwAROe4hURR21EODtRgXQv5bv9li05W1eiG+m/gUCg7EMCSJ89Nty6RFDs0Tc/QbPf1LmSSNzHRBHkvAzgDBY/9hoRIZmQ3ByWfUeC6JEJjt+P/TtZUUZ8AL7HANPivCZjbemXGN+4l4qTiqAl/7QVsBOYqhdDMLb+H7Ir2qWsnN1C/TacS60hG5+wDxv3RVt7utUxRDL0io7PEgbRYM2SgdwSjuyXc26A55pKwxGLJxJtrH6icZ4D8LYdMSYDqSVDFtI+eAjO9UysZCwpEMM9oLlChICCEhoAvU69b+QGfvoy3pfy5O/xqXB06DUOCOp2fTXCkr5BPe1A3ugI/WDS1uqBAUUGxukuEHDp2WZM28hLs49AlDd4yCSWji+TQEfbqvhmf1z/UGQOcW2yetbBKBQ9fQWzMDHODN2to7Gxu540YhSzzqtUBD/SWc5Us/mLFIyulJrlbKYSBn+klfLTZ1eC/cu1hz2AalAeXeSqhjQudksuEVJO7kDOclksuSg09N3xca/5yCQF2qpMFZE1neNcdcerDABT3HBV0fpnWF858URPjft6XK0Tz6OM3RkK4iVv26zLNtpGjaqcyIcke6kzB/DnZu9gJY8jIZRpFaBilgtOSvYwXsDwIDAQABGazMZ3NoYS0yNTYZrNBnc2hhLTI1Nhmszlgg6iHM4buDbKeXhKwRov4ojGceX2LCiDcY7Y2lmUeuKLEZrM+EWCA3pKBP5MshqAqBQZ6091kvcnEGlyf7XrhZ/RtQMZv65FggAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABYIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWCAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFkCAE3UOgzQgssusjyzjdyaOElJKlqkhAfPQvO8CnD2fRuEEBhQzktkX6ad5rCwzlI3LrCmog7JFiGyexVw0ZWZkWttsF9wiGiE87AAnB3Bu7CduuE257ZvHhXNXMh6odgsS2SXDkB531ReomZp7lN0Rwg+LfYVBsVT5vSSCi8ixduL6DqH3CdEr9d30cDw5BiDYF0rj13q87U/+euosu3kmF0ROsMsOHkugEk+ztEvbfjKqEpKNRUvMsw52ntMRv+Zqe7Ib6DWYdiYAHHufrhAXPIX5oF8TNrZ5iK3Dq62Gmb0mbvd4FQ8l7RuCoJF/+SIM54lTAF/o30ww3Fch6m5zejK8Qyx0KDjAN+6C60j4Z0sAP3qINwMq5I5jYo0Gxf6wYItcpXeHE26xtoWatbW043ZryE6js/qwuoXmNc1xE2b3NACg6P/YB4uFsK2bmIqear0/uNkOO5Fxs2bi754hM2T/vWOuyvgma0IWdmRkrWt/kYz+vwMsx2vnSxopOivTn6yAEwWrSSa9X7fV3k+lcVrfCjCeDrr5MrSbd+GJp5GKbG74oSdY0xlJXJEZ7HM/UBOJ0A0K8VeShdyhK7zj2H6WMmm14UDUnMqK2iScjIO+NYD8ycZG7gzKRWObCbRgMj7w6eBpbWDXHwvaOB9DHapQN7PHqk5+jTFDH9JQo5b"
  },
  "policy_ids":[],
  "nonce":"81a22025655341608f09656bcc30f1e9"
}'
```
调用时必须带上nonce参数


## 3. getsecret

### 3.1 添加秘密
秘密可以是任意的json
```shell
echo "{\"key\":\"123\"}" |base64
```
添加秘密
```shell
curl --location --request POST 'http://127.0.0.1:8081/v1/secret' \
--header 'Content-Type: application/json' \
--header 'Authorization: <login token>' \
--data-raw '{
    "secret":"eyJrZXkiOiIxMjMifQo=",
    "name":"test-vcca"
}'
```
添加成果会返回id
```shell
{
    "code": 200,
    "message": "add secret successful",
    "id": "0d6f1080-dcf4-4961-8def-9fb1f98d6174"
}
```

获取秘密必须添加nonce， 参考2.2，添加nonce，并且签署report， 然后调用getsecret接口
```shell
curl --location --request POST 'http://127.0.0.1:8081/v1/attest/getsecret' \
--header 'SecretId: 0d6f1080-dcf4-4961-8def-9fb1f98d6174' \
--header 'API-KEY: <apikey>' \
--header 'Content-Type: application/json' \
--data-raw '{
  "report": {
    "tee": "virtcca",
    "parameter": {
      "x5c": "MIIEjjCCA3agAwIBAgIREU42SbPDXIIcRtuLWsjAufUwDQYJKoZIhvcNAQELBQAwPTELMAkGA1UEBhMCQ04xDzANBgNVBAoTBkh1YXdlaTEdMBsGA1UEAxMUSHVhd2VpIElUIFByb2R1Y3QgQ0EwHhcNMjQwNTI4MDIxNDE1WhcNMzkwNTI1MDIxNDE1WjA2MQswCQYDVQQGEwJDTjEPMA0GA1UEChMGSHVhd2VpMRYwFAYDVQQDEw0xMDIzQTc5ODkxMzhUMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEApmLUgbAe8uxQTAi0FNJPQZFW9j1wYZ/ezG2sdwmo+K3WZapOnJcAETnuIVEUdtRDg7UYF0L+W7/ZYtOVtXohvpv4FAoOxDAkifPTbcukRQ7NE3P0Gz39S5kkjcx0QR5LwM4AwWP/YaESGZkNwcln1HguiRCY7fj/07WVFGfAC+xwDT4rwmY23plxjfuJeKk4qgJf+0FbATmKoXQzC2/h+yK9qlrJzdQv02nEutIRufsA8b90Vbe7rVMUQy9IqOzxIG0WDNkoHcEo7sl3NugOeaSsMRiycSbax+onGeA/C2HTEmA6klQxbSPngIzvVMrGQsKRDDPaC5QoSAghIaAL1OvW/kBn76Mt6X8uTv8alwdOg1Dgjqdn01wpK+QT3tQN7oCP1g0tbqgQFFBsbpLhBw6dlmTNvIS7OPQJQ3eMgklo4vk0BH26r4Zn9c/1BkDnFtsnrWwSgUPX0FszAxzgzdraOxsbueNGIUs86rVAQ/0lnOVLP5ixSMrpSa5WymEgZ/pJXy02dXgv3LtYc9gGpQHl3kqoY0LnZLLhFSTu5AznJZLLkoNPTd8XGv+cgkBdqqTBWRNZ3jXHXHqwwAU9xwVdH6Z1hfOfFET437elytE8+jjN0ZCuIlb9usyzbaRo2qnMiHJHupMwfw52bvYCWPIyGUaRWgYpYLTkr2MF7A8CAwEAAaOBjzCBjDAfBgNVHSMEGDAWgBQSijfs+XNX1+SDurVvA+zdrhFOzzALBgNVHQ8EBAMCA/gwXAYIKwYBBQUHAQEEUDBOMCgGCCsGAQUFBzAChhxodHRwOi8vMTI3LjAuMC4xL2NhaXNzdWUuaHRtMCIGCCsGAQUFBzABhhZodHRwOi8vMTI3LjAuMC4xOjIwNDQzMA0GCSqGSIb3DQEBCwUAA4IBAQAsiyQZ7V9bOZDaFg9atpKWzV9B0gf1rtdTo2MNzFs1r63N2ZprUmQc649TJ3l5INcG0GHbdDqvvHpAVB41nlqXQPqAziHXp7v4YgQxXNnbDVt2mJIKcr2S6Ox9N59IqckSQouanmRmjbW7g90gsIS55rVH2QfuFgCpnEOf6MBEvWAU37omKFF6j56Cv/lMMNLsMzJgsE6dXwLEjDqykoB3ceantt/YSNZbltiF+zSlSBOdAD5T+uoquTP6cW4VIkmDJ8G9/pZaLJ4JycmwLtkuzjm3+c0vXPa88rOdpz3mO07BqsXYfSPXWMH208QxPwyQUbmyQwS5OSoeuRvGw22i"
    },
    "tee_report": "2QGPoRms0VkFjdKERKEBOCSgWQN/WQN8pwpYQB+rVhlgPiYD0ZH5b9+p4XJyUA/3qrpkEnfnNkdJIaQxThkGQ2hlWTR7ITHLvl/rOiBjQND1JYDf6HKTZw/Jv0MZrMtYQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAZrM1ZAiYwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQCmYtSBsB7y7FBMCLQU0k9BkVb2PXBhn97Mbax3Caj4rdZlqk6clwAROe4hURR21EODtRgXQv5bv9li05W1eiG+m/gUCg7EMCSJ89Nty6RFDs0Tc/QbPf1LmSSNzHRBHkvAzgDBY/9hoRIZmQ3ByWfUeC6JEJjt+P/TtZUUZ8AL7HANPivCZjbemXGN+4l4qTiqAl/7QVsBOYqhdDMLb+H7Ir2qWsnN1C/TacS60hG5+wDxv3RVt7utUxRDL0io7PEgbRYM2SgdwSjuyXc26A55pKwxGLJxJtrH6icZ4D8LYdMSYDqSVDFtI+eAjO9UysZCwpEMM9oLlChICCEhoAvU69b+QGfvoy3pfy5O/xqXB06DUOCOp2fTXCkr5BPe1A3ugI/WDS1uqBAUUGxukuEHDp2WZM28hLs49AlDd4yCSWji+TQEfbqvhmf1z/UGQOcW2yetbBKBQ9fQWzMDHODN2to7Gxu540YhSzzqtUBD/SWc5Us/mLFIyulJrlbKYSBn+klfLTZ1eC/cu1hz2AalAeXeSqhjQudksuEVJO7kDOclksuSg09N3xca/5yCQF2qpMFZE1neNcdcerDABT3HBV0fpnWF858URPjft6XK0Tz6OM3RkK4iVv26zLNtpGjaqcyIcke6kzB/DnZu9gJY8jIZRpFaBilgtOSvYwXsDwIDAQABGazMZ3NoYS0yNTYZrNBnc2hhLTI1Nhmszlgg6iHM4buDbKeXhKwRov4ojGceX2LCiDcY7Y2lmUeuKLEZrM+EWCA3pKBP5MshqAqBQZ6091kvcnEGlyf7XrhZ/RtQMZv65FggAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABYIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWCAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFkCAE3UOgzQgssusjyzjdyaOElJKlqkhAfPQvO8CnD2fRuEEBhQzktkX6ad5rCwzlI3LrCmog7JFiGyexVw0ZWZkWttsF9wiGiE87AAnB3Bu7CduuE257ZvHhXNXMh6odgsS2SXDkB531ReomZp7lN0Rwg+LfYVBsVT5vSSCi8ixduL6DqH3CdEr9d30cDw5BiDYF0rj13q87U/+euosu3kmF0ROsMsOHkugEk+ztEvbfjKqEpKNRUvMsw52ntMRv+Zqe7Ib6DWYdiYAHHufrhAXPIX5oF8TNrZ5iK3Dq62Gmb0mbvd4FQ8l7RuCoJF/+SIM54lTAF/o30ww3Fch6m5zejK8Qyx0KDjAN+6C60j4Z0sAP3qINwMq5I5jYo0Gxf6wYItcpXeHE26xtoWatbW043ZryE6js/qwuoXmNc1xE2b3NACg6P/YB4uFsK2bmIqear0/uNkOO5Fxs2bi754hM2T/vWOuyvgma0IWdmRkrWt/kYz+vwMsx2vnSxopOivTn6yAEwWrSSa9X7fV3k+lcVrfCjCeDrr5MrSbd+GJp5GKbG74oSdY0xlJXJEZ7HM/UBOJ0A0K8VeShdyhK7zj2H6WMmm14UDUnMqK2iScjIO+NYD8ycZG7gzKRWObCbRgMj7w6eBpbWDXHwvaOB9DHapQN7PHqk5+jTFDH9JQo5b"
  }，
  "policy_ids":[],
  "nonce":"81a22025655341608f09656bcc30f1e9"
}'
```
> 注意在header中需要添加SecretId参数

认证成功,即返回秘密
```shell
{
    "code": 200,
    "message": "get secret info successful",
    "secret": {
        "key": "123"
    }
}
```



## getcert

生成公私钥，并获取公钥的base64值
```shell
openssl ecparam -genkey -name prime256v1 -noout -out private_key.pem
openssl ec -in private_key.pem -pubout -out public_key.pem
cat public_key.pem |base64 -w 0
```
> 注意：目前支持ECC256算法

```shell
curl --location --request POST 'http://127.0.0.1:8081/v1/attest/getcert' \
--header 'SecretId: 0d6f1080-dcf4-4961-8def-9fb1f98d6174' \
--header 'API-KEY: <apikey>'
--header 'Content-Type: application/json' \
--data-raw '{
    "attestInfo": {
        "report": {
            "tee": "virtcca",
            "parameter": {
                "x5c": "MIIEjjCCA3agAwIBAgIREU42SbPDXIIcRtuLWsjAufUwDQYJKoZIhvcNAQELBQAwPTELMAkGA1UEBhMCQ04xDzANBgNVBAoTBkh1YXdlaTEdMBsGA1UEAxMUSHVhd2VpIElUIFByb2R1Y3QgQ0EwHhcNMjQwNTI4MDIxNDE1WhcNMzkwNTI1MDIxNDE1WjA2MQswCQYDVQQGEwJDTjEPMA0GA1UEChMGSHVhd2VpMRYwFAYDVQQDEw0xMDIzQTc5ODkxMzhUMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEApmLUgbAe8uxQTAi0FNJPQZFW9j1wYZ/ezG2sdwmo+K3WZapOnJcAETnuIVEUdtRDg7UYF0L+W7/ZYtOVtXohvpv4FAoOxDAkifPTbcukRQ7NE3P0Gz39S5kkjcx0QR5LwM4AwWP/YaESGZkNwcln1HguiRCY7fj/07WVFGfAC+xwDT4rwmY23plxjfuJeKk4qgJf+0FbATmKoXQzC2/h+yK9qlrJzdQv02nEutIRufsA8b90Vbe7rVMUQy9IqOzxIG0WDNkoHcEo7sl3NugOeaSsMRiycSbax+onGeA/C2HTEmA6klQxbSPngIzvVMrGQsKRDDPaC5QoSAghIaAL1OvW/kBn76Mt6X8uTv8alwdOg1Dgjqdn01wpK+QT3tQN7oCP1g0tbqgQFFBsbpLhBw6dlmTNvIS7OPQJQ3eMgklo4vk0BH26r4Zn9c/1BkDnFtsnrWwSgUPX0FszAxzgzdraOxsbueNGIUs86rVAQ/0lnOVLP5ixSMrpSa5WymEgZ/pJXy02dXgv3LtYc9gGpQHl3kqoY0LnZLLhFSTu5AznJZLLkoNPTd8XGv+cgkBdqqTBWRNZ3jXHXHqwwAU9xwVdH6Z1hfOfFET437elytE8+jjN0ZCuIlb9usyzbaRo2qnMiHJHupMwfw52bvYCWPIyGUaRWgYpYLTkr2MF7A8CAwEAAaOBjzCBjDAfBgNVHSMEGDAWgBQSijfs+XNX1+SDurVvA+zdrhFOzzALBgNVHQ8EBAMCA/gwXAYIKwYBBQUHAQEEUDBOMCgGCCsGAQUFBzAChhxodHRwOi8vMTI3LjAuMC4xL2NhaXNzdWUuaHRtMCIGCCsGAQUFBzABhhZodHRwOi8vMTI3LjAuMC4xOjIwNDQzMA0GCSqGSIb3DQEBCwUAA4IBAQAsiyQZ7V9bOZDaFg9atpKWzV9B0gf1rtdTo2MNzFs1r63N2ZprUmQc649TJ3l5INcG0GHbdDqvvHpAVB41nlqXQPqAziHXp7v4YgQxXNnbDVt2mJIKcr2S6Ox9N59IqckSQouanmRmjbW7g90gsIS55rVH2QfuFgCpnEOf6MBEvWAU37omKFF6j56Cv/lMMNLsMzJgsE6dXwLEjDqykoB3ceantt/YSNZbltiF+zSlSBOdAD5T+uoquTP6cW4VIkmDJ8G9/pZaLJ4JycmwLtkuzjm3+c0vXPa88rOdpz3mO07BqsXYfSPXWMH208QxPwyQUbmyQwS5OSoeuRvGw22i"
            },
            "tee_report": "2QGPoRms0VkFjdKERKEBOCSgWQN/WQN8pwpYQB+rVhlgPiYD0ZH5b9+p4XJyUA/3qrpkEnfnNkdJIaQxThkGQ2hlWTR7ITHLvl/rOiBjQND1JYDf6HKTZw/Jv0MZrMtYQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAZrM1ZAiYwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQCmYtSBsB7y7FBMCLQU0k9BkVb2PXBhn97Mbax3Caj4rdZlqk6clwAROe4hURR21EODtRgXQv5bv9li05W1eiG+m/gUCg7EMCSJ89Nty6RFDs0Tc/QbPf1LmSSNzHRBHkvAzgDBY/9hoRIZmQ3ByWfUeC6JEJjt+P/TtZUUZ8AL7HANPivCZjbemXGN+4l4qTiqAl/7QVsBOYqhdDMLb+H7Ir2qWsnN1C/TacS60hG5+wDxv3RVt7utUxRDL0io7PEgbRYM2SgdwSjuyXc26A55pKwxGLJxJtrH6icZ4D8LYdMSYDqSVDFtI+eAjO9UysZCwpEMM9oLlChICCEhoAvU69b+QGfvoy3pfy5O/xqXB06DUOCOp2fTXCkr5BPe1A3ugI/WDS1uqBAUUGxukuEHDp2WZM28hLs49AlDd4yCSWji+TQEfbqvhmf1z/UGQOcW2yetbBKBQ9fQWzMDHODN2to7Gxu540YhSzzqtUBD/SWc5Us/mLFIyulJrlbKYSBn+klfLTZ1eC/cu1hz2AalAeXeSqhjQudksuEVJO7kDOclksuSg09N3xca/5yCQF2qpMFZE1neNcdcerDABT3HBV0fpnWF858URPjft6XK0Tz6OM3RkK4iVv26zLNtpGjaqcyIcke6kzB/DnZu9gJY8jIZRpFaBilgtOSvYwXsDwIDAQABGazMZ3NoYS0yNTYZrNBnc2hhLTI1Nhmszlgg6iHM4buDbKeXhKwRov4ojGceX2LCiDcY7Y2lmUeuKLEZrM+EWCA3pKBP5MshqAqBQZ6091kvcnEGlyf7XrhZ/RtQMZv65FggAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABYIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWCAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFkCAE3UOgzQgssusjyzjdyaOElJKlqkhAfPQvO8CnD2fRuEEBhQzktkX6ad5rCwzlI3LrCmog7JFiGyexVw0ZWZkWttsF9wiGiE87AAnB3Bu7CduuE257ZvHhXNXMh6odgsS2SXDkB531ReomZp7lN0Rwg+LfYVBsVT5vSSCi8ixduL6DqH3CdEr9d30cDw5BiDYF0rj13q87U/+euosu3kmF0ROsMsOHkugEk+ztEvbfjKqEpKNRUvMsw52ntMRv+Zqe7Ib6DWYdiYAHHufrhAXPIX5oF8TNrZ5iK3Dq62Gmb0mbvd4FQ8l7RuCoJF/+SIM54lTAF/o30ww3Fch6m5zejK8Qyx0KDjAN+6C60j4Z0sAP3qINwMq5I5jYo0Gxf6wYItcpXeHE26xtoWatbW043ZryE6js/qwuoXmNc1xE2b3NACg6P/YB4uFsK2bmIqear0/uNkOO5Fxs2bi754hM2T/vWOuyvgma0IWdmRkrWt/kYz+vwMsx2vnSxopOivTn6yAEwWrSSa9X7fV3k+lcVrfCjCeDrr5MrSbd+GJp5GKbG74oSdY0xlJXJEZ7HM/UBOJ0A0K8VeShdyhK7zj2H6WMmm14UDUnMqK2iScjIO+NYD8ycZG7gzKRWObCbRgMj7w6eBpbWDXHwvaOB9DHapQN7PHqk5+jTFDH9JQo5b",
            "runtime_data": "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUZrd0V3WUhLb1pJemowQ0FRWUlLb1pJemowREFRY0RRZ0FFOEhOUVBBL00xQ0xlZGFJU1c2cUxsRWlJNGsxWgpvZ1EzUWg2SGVRVFhvRzZIRkJqQ2hHL09RdnRlSnFwMVZCN0VyMC9MakplK2tPUHRHVStiK2ZjeTBnPT0KLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg=="
        },
        "nonce":"25b2e08f99f54c50953310f04a4c2f40"
    },
    "csr": {
        "common_name": "************",
        "expiration": 10
    }
}'
```
`attestInfo`是认证报告信息，其中必须包含nonce和runtime_data, runtime_data即公钥的base64值， 同时在硬件报告的userData必须为sha256（nonce+runtime_data)值

`csr`是证书的信息，目前仅仅开放以下字段
+ common_name： 通用名，通常为域名或ip
+ ip_addresses： SAN扩展中的IP信息，如果common_name 是IP会自动填充
+ expiration： 过期时间，单位（年）

响应
```shell
{
    "code": 200,
    "message": "attest successful",
    "data": {
        "x5c": [
            "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        ],
        "serial_number": "1721986857"
    }
}
```
证书是x5c格式，签发证书时在OID **********.4.9中嵌入了该硬件报告的token，用户可进一步查看硬件信息

证书的根CA与验证token的CA相同，都可以通过接口获取
```shell
curl http://127.0.0.1:8081/v1/pki/ca
```

如下为一个证书的信息示例：
```shell
Certificate:
    Data:
        Version: 3 (0x2)
        Serial Number: 1721986857 (0x66a36f29)
        Signature Algorithm: ecdsa-with-SHA256
        Issuer: C=CN, ST=Zhejiang, L=Jiaxing, O=Nanhulab, OU=Nanhulab, CN=Trust Cluster Attestation Server Root CA
        Validity
            Not Before: Jul 26 09:40:57 2024 GMT
            Not After : Jul 26 09:40:57 2034 GMT
        Subject: CN=************
        Subject Public Key Info:
            Public Key Algorithm: id-ecPublicKey
                Public-Key: (256 bit)
                pub:
                    04:f0:73:50:3c:0f:cc:d4:22:de:75:a2:12:5b:aa:
                    8b:94:48:88:e2:4d:59:a2:04:37:42:1e:87:79:04:
                    d7:a0:6e:87:14:18:c2:84:6f:ce:42:fb:5e:26:aa:
                    75:54:1e:c4:af:4f:cb:8c:97:be:90:e3:ed:19:4f:
                    9b:f9:f7:32:d2
                ASN1 OID: prime256v1
                NIST CURVE: P-256
        X509v3 extensions:
            X509v3 Key Usage: critical
                Digital Signature, Key Encipherment
            X509v3 Extended Key Usage: 
                TLS Web Server Authentication
            X509v3 Basic Constraints: critical
                CA:FALSE
            X509v3 Authority Key Identifier: 
                62:DA:D1:0A:C7:35:81:54:93:CF:2B:C0:46:F7:43:C1:6A:11:33:C2
            X509v3 Subject Alternative Name: 
                IP Address:************
            **********.4.9: 
                ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    Signature Algorithm: ecdsa-with-SHA256
    Signature Value:
        30:45:02:21:00:b4:9c:cc:6b:cf:f4:a3:78:c5:9f:c9:63:8f:
        2a:43:6a:4a:6d:e2:45:4a:57:e8:c9:ea:a1:c4:a5:3e:6f:4c:
        d8:02:20:2c:d2:2b:51:40:b2:ea:74:45:d7:40:ac:f6:3e:a0:
        8b:00:f4:f9:4a:83:9d:6f:3b:61:3f:79:ec:23:a6:4d:5c
```
