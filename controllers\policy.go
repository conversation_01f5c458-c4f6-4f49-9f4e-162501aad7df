package controllers

import (
	"encoding/json"
	"fmt"
	"github.com/beego/beego/v2/core/logs"
	"tcas/consts"
	"tcas/models"
)

type PolicyController struct {
	DefaultController
}

func (uc *PolicyController) Prepare() {
	uc.DefaultController.Prepare()
	if !models.HasPolicyPermission(uc.User.Roles) {
		message := fmt.Sprintf("no permission to manager policy")
		errMes := uc.HandleErrorStatusBadRequest(401, message, "")
		logs.Error(errMes)
		return
	}
}

func (uc *PolicyController) CreatePolicy() {
	var policy models.Policy
	if err := json.Unmarshal(uc.Ctx.Input.RequestBody, &policy); err != nil {
		message := fmt.Sprintf("get body error. %+v", err)
		errMes := uc.HandleErrorStatusBadRequest(consts.ERROR_CODE__JSON__UNMARSHAL_FAILED, message, "")
		logs.Error(errMes)
		return
	}

	policy.UserId = uc.User.Id
	id, err := models.PolicyModel.AddPolicy(&policy)
	if err != nil {
		message := fmt.Sprintf("create policy failed, error: %s", err.Error())
		errMes := uc.HandleErrorStatusBadRequest(consts.ERROR_CODE_CREATE_POLICY_FAILED, message, "")
		logs.Error(errMes)
		return
	}

	mesg, baseRes := uc.HandleSuccess(200, "add policy successful", "")

	res := PolicyIdRes{
		*baseRes,
		id,
	}

	uc.Data["json"] = res
	uc.ServeJSON()
	logs.Info(mesg)
}

func (uc *PolicyController) DeletePolicy() {
	policyId := uc.GetString(":policyId")
	if policyId == "" {
		message := fmt.Sprintf("policy id is null")
		errMes := uc.HandleErrorStatusBadRequest(consts.ERROR_CODE_DELETE_POLICY_FAILED, message, "")
		logs.Error(errMes)
		return
	}

	policy, err := models.PolicyModel.GetPolicyById(policyId)
	if err != nil {
		message := fmt.Sprintf("policy does not exist")
		errMes := uc.HandleErrorStatusBadRequest(consts.ERROR_CODE_DELETE_POLICY_FAILED, message, "")
		logs.Error(errMes)
		return
	}

	if policy.UserId != uc.User.Id {
		message := fmt.Sprintf("policy does not exist")
		errMes := uc.HandleErrorStatusBadRequest(consts.ERROR_CODE_DELETE_POLICY_FAILED, message, "")
		logs.Error(errMes)
		return
	}

	err = models.PolicyModel.DeletePolicy(policyId)
	if err != nil {
		message := fmt.Sprintf("delete policy failed, error: %s", err)
		errMes := uc.HandleErrorStatusBadRequest(consts.ERROR_CODE_DELETE_POLICY_FAILED, message, fmt.Sprintf("policy id is %s", policyId))
		logs.Error(errMes)
		return
	}

	mesg, baseRes := uc.HandleSuccess(200, "delete report successful", "")

	res := PolicyIdRes{
		*baseRes,
		policyId,
	}

	uc.Data["json"] = res
	uc.ServeJSON()
	logs.Info(mesg)
}

func (uc *PolicyController) GetPolicyList() {
	at := uc.GetString("attestation")
	logs.Debug(uc.User.Id)
	list, err := models.PolicyModel.GetPolicies(at, uc.User.Id)
	if err != nil {
		logs.Error("get policy list failed, error: %s", err)
	}
	mesg, baseRes := uc.HandleSuccess(200, "get policy info successful", "")
	logs.Info(mesg)
	logs.Debug(list)
	res := DataRes{
		*baseRes,
		list,
	}
	uc.Data["json"] = res
	uc.ServeJSON()
	return
}
