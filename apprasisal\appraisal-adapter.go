package apprasisal

import (
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/beego/beego/v2/core/logs"
	"tcas/models"
	"tcas/policyengine"
	"tcas/tees"
	"tcas/token"
	"tcas/util/convertor"
)

type TrustDeviceReport struct {
	DeviceType   string      `json:"type"`
	DeviceReport string      `json:"device_report"`
	Parameter    interface{} `json:"parameter"`
}

type NodeEvidence struct {
	Tee         string               `json:"tee"`
	TeeReport   string               `json:"tee_report"`
	Parameter   interface{}          `json:"parameter"`
	TrustDevice []*TrustDeviceReport `json:"trust_devices"`
	RuntimeData string               `json:"runtime_data"`
	InitData    string               `json:"init_data"`
	EventLog    string               `json:"event_log"`
}

type NodeAttestInfo struct {
	Report    *NodeEvidence    `json:"report"`
	Nonce     string           `json:"nonce"`
	PolicyIds []string         `json:"policy_ids"`
	Policies  []*models.Policy `json:"-"`
}

type ClusterAttestInfo struct {
	Reports   []*NodeEvidence  `json:"reports"`
	Nonce     string           `json:"nonce"`
	PolicyIds []string         `json:"policy_ids"`
	Policies  []*models.Policy `json:"-"`
}

type TrustNodeVerifiedResult struct {
	Tee         tees.VerifyTeeResult   `json:"tee"`
	TrustDevice []tees.VerifyTeeResult `json:"trust_device"`
}

type TrustNodeClaims struct {
	TrustNode    *TrustNodeVerifiedResult `json:"trust_node"`
	PolicyInfos  []*models.Policy         `json:"policy_info"`
	AttesterInfo *AttesterInfo            `json:"attester_info"`
}

type AttesterInfo struct {
	BinRuntimeData  string                 `json:"runtimedata_bin,omitempty"`
	JsonRuntimeData map[string]interface{} `json:"runtimedata_json,omitempty"`
	VerifyNonce     string                 `json:"verify_nonce,omitempty"`
}

type TrustClusterClaims struct {
	TrustNodes   []*TrustNodeVerifiedResult
	PolicyInfos  []*models.Policy
	AttesterInfo *AttesterInfo
}

func UserDataCheck(attestResult *TrustNodeVerifiedResult, nonce, runtimeData string) (*AttesterInfo, error) {
	if nonce == "" && runtimeData == "" {
		logs.Info("skip user data check")
		return nil, nil
	}

	results := make([]tees.VerifyTeeResult, 0)
	results = append(results, attestResult.Tee)
	//todo check trusted device
	//results = append(results, attestResult.TrustDevice...)

	attesterInfo := new(AttesterInfo)
	for _, r := range results {
		if tee, existed := r["type"]; existed {
			teeType := fmt.Sprintf("%s", tee)
			if !tees.IsVerifierExists(teeType) {
				return nil, fmt.Errorf("tee type [%s] is not support now", teeType)
			}

			userData, err := tees.Verifiers[teeType].GetUserData(attestResult.Tee)
			if err != nil {
				return nil, fmt.Errorf("get user data faield, error: %s", err)
			}

			if nonce != "" {
				if err := GNonceManager.CheckNonce(nonce); err != nil {
					return nil, err
				}
				attesterInfo.VerifyNonce = nonce
			}

			logs.Debug("user data is %s", userData)
			expectedUserData := fmt.Sprintf("%x", sha256.Sum256([]byte(nonce+runtimeData)))
			logs.Debug("expected data is %s", expectedUserData)
			if len(userData) > 64 {
				userData = userData[:64]
			}
			if userData != expectedUserData {
				return nil, fmt.Errorf("userData is not correct")
			}
		} else {
			return nil, fmt.Errorf("verify tee result format error")
		}
	}
	if runtimeData != "" {
		rbyte, err := base64.StdEncoding.DecodeString(runtimeData)
		if err != nil {
			return nil, fmt.Errorf("runtime data is not base64 format")
		}

		var runTimeDataJson map[string]interface{}
		err = json.Unmarshal(rbyte, &runTimeDataJson)
		if err != nil {
			attesterInfo.BinRuntimeData = runtimeData
		} else {
			logs.Info("runtime data is json format")
			attesterInfo.JsonRuntimeData = runTimeDataJson
		}
	}
	return attesterInfo, nil
}

func TrustNodeAppraisal(attestInfo *NodeAttestInfo) (signedToken string, err error) {
	if attestInfo == nil {
		return "", fmt.Errorf("attestInfo is nil")
	}

	verifyTeeResult, err := NodeVerify(attestInfo.Report)
	if err != nil {
		return "", fmt.Errorf("node verify failed, error: %s", err)
	}

	attesterInfo, err := UserDataCheck(verifyTeeResult, attestInfo.Nonce, attestInfo.Report.RuntimeData)
	if err != nil {
		logs.Error("user data check failed, error:%s", err)
		return "", fmt.Errorf("user data or nonce not correct")
	}

	trustNodeClaims, err := TrustNodePolicyEvaluate(attestInfo.Policies, verifyTeeResult)
	if err != nil {
		return "", fmt.Errorf("policy evaluate failed, error: %s", err)
	}

	trustNodeClaims.AttesterInfo = attesterInfo
	claims, err := convertor.AnyToMap(trustNodeClaims)
	if err != nil {
		return "", fmt.Errorf("convert trust node cliams to map failed, error:%s", err)
	}

	finalToken, err := token.GenerateToken(claims)
	if err != nil {
		errMsg := fmt.Sprintf("generate token failed, error: %s", err)
		logs.Error(errMsg)
		return "", fmt.Errorf(errMsg)
	}

	logs.Debug("token is %s", finalToken)

	return finalToken, nil

}

func NodeVerify(report *NodeEvidence) (*TrustNodeVerifiedResult, error) {
	if report == nil {
		return nil, fmt.Errorf("the report in evidence is nil")
	}

	reportData, err := base64.StdEncoding.DecodeString(report.TeeReport)
	if err != nil {
		return nil, fmt.Errorf("base64 decode report failed, error:%s", err)
	}

	if !tees.IsVerifierExists(report.Tee) {
		return nil, fmt.Errorf("tee type [%s] is not support now", report.Tee)
	}

	teeVerifyResult, err := tees.Verifiers[report.Tee].VerifyTeeReport(reportData, report.Parameter)
	if err != nil {
		return nil, fmt.Errorf("verify tee report failed, error: %s", err)
	}
	teeVerifyResult["type"] = report.Tee
	logs.Debug("verify result is %v", teeVerifyResult)

	deviceVerifyResults := make([]tees.VerifyTeeResult, 0)
	for _, d := range report.TrustDevice {
		r, err := TrustDeviceVerify(d)
		if err != nil {
			return nil, fmt.Errorf("verify trust device failed, error: %s", err)
		}
		deviceVerifyResults = append(deviceVerifyResults, r)
	}

	return &TrustNodeVerifiedResult{
		Tee:         teeVerifyResult,
		TrustDevice: deviceVerifyResults,
	}, nil
}

func TrustDeviceVerify(t *TrustDeviceReport) (tees.VerifyTeeResult, error) {
	if t == nil {
		return nil, fmt.Errorf("the trust device report is nil")
	}

	reportData, err := base64.StdEncoding.DecodeString(t.DeviceReport)
	if err != nil {
		return nil, fmt.Errorf("base64 decode device report failed, error:%s", err)
	}

	if !tees.IsVerifierExists(t.DeviceType) {
		return nil, fmt.Errorf("tee device [%s] is not support now", t.DeviceType)
	}

	verifyResult, err := tees.Verifiers[t.DeviceType].VerifyTeeReport(reportData, t.Parameter)
	if err != nil {
		return nil, fmt.Errorf("verify tee report failed, error: %s", err)
	}
	verifyResult["type"] = t.DeviceType
	logs.Debug("verify result is %v", verifyResult)

	return verifyResult, nil
}

func TrustNodePolicyEvaluate(policies []*models.Policy, trustNode *TrustNodeVerifiedResult) (*TrustNodeClaims, error) {
	input := struct {
		TrustNode *TrustNodeVerifiedResult `json:"trust_node"`
	}{
		trustNode,
	}

	trustNodeClaims := new(TrustNodeClaims)
	trustNodeClaims.TrustNode = trustNode
	for _, policy := range policies {
		policyString, err := base64.StdEncoding.DecodeString(policy.Rego)
		if err != nil {
			return nil, fmt.Errorf("decode the rego policy of %s failed, error:%s", policy.Id, err)
		}

		logs.Debug("policy is %s", policy.Rego)
		pe := policyengine.NewPolicyExecutor("data.trustnode", string(policyString))
		regoResultSet, err := pe.Evaluate(input)
		if err != nil {
			errmsg := fmt.Sprintf("policy evaluate failed, error:%s", err)
			logs.Error(errmsg)
			return nil, fmt.Errorf(errmsg)
		}
		logs.Debug("policy evaluate result is %v", regoResultSet[0].Expressions[0].Value)
		regoResult, err := convertor.AnyToMap(regoResultSet[0].Expressions[0].Value)
		if err != nil {
			errmsg := fmt.Sprintf("rego result format error")
			logs.Error(errmsg)
			return nil, fmt.Errorf(errmsg)
		}

		if regoResult[models.NodeType] == false {
			errmsg := fmt.Sprintf("policy [%s] does not match", policy.Id)
			logs.Error(errmsg)
			return nil, fmt.Errorf(errmsg)
		}

		policyInfo := models.Policy{
			Id:        policy.Id,
			Hash:      policy.Hash,
			Signature: policy.Signature,
		}
		trustNodeClaims.PolicyInfos = append(trustNodeClaims.PolicyInfos, &policyInfo)
	}

	return trustNodeClaims, nil
}

func TrustClusterPolicyEvaluate(policies []*models.Policy, trustNodes []*TrustNodeVerifiedResult) (*TrustClusterClaims, error) {
	input := struct {
		TrustCluster []*TrustNodeVerifiedResult `json:"trust_cluster"`
	}{
		trustNodes,
	}

	trustClusterClaims := new(TrustClusterClaims)
	trustClusterClaims.TrustNodes = trustNodes
	for _, policy := range policies {
		policyString, err := base64.StdEncoding.DecodeString(policy.Rego)
		if err != nil {
			return nil, fmt.Errorf("decode the rego policy of %s failed, error:%s", policy.Id, err)
		}

		logs.Debug("policy is %s", policy.Rego)
		pe := policyengine.NewPolicyExecutor("data.trustcluster", string(policyString))
		regoResultSet, err := pe.Evaluate(input)
		if err != nil {
			errmsg := fmt.Sprintf("policy evaluate failed, error:%s", err)
			logs.Error(errmsg)
			return nil, fmt.Errorf(errmsg)
		}
		logs.Debug("policy evaluate result is %v", regoResultSet[0].Expressions[0].Value)
		regoResult, err := convertor.AnyToMap(regoResultSet[0].Expressions[0].Value)
		if err != nil {
			errmsg := fmt.Sprintf("rego result format error")
			logs.Error(errmsg)
			return nil, fmt.Errorf(errmsg)
		}

		if regoResult[models.NodeType] == false {
			errmsg := fmt.Sprintf("policy [%s] does not match", policy.Id)
			logs.Error(errmsg)
			return nil, fmt.Errorf(errmsg)
		}

		policyInfo := models.Policy{
			Id:        policy.Id,
			Hash:      policy.Hash,
			Signature: policy.Signature,
		}
		trustClusterClaims.PolicyInfos = append(trustClusterClaims.PolicyInfos, &policyInfo)
	}

	return trustClusterClaims, nil
}

func TrustClusterVerify(attestInfo *ClusterAttestInfo) (signedToken string, err error) {
	if attestInfo == nil {
		return "", fmt.Errorf("attestInfo is nil")
	}

	nodeVerifyResults := make([]*TrustNodeVerifiedResult, 0)
	for _, node := range attestInfo.Reports {
		verifyResult, err := NodeVerify(node)
		if err != nil {
			return "", fmt.Errorf("node verify failed, error: %s", err)
		}
		nodeVerifyResults = append(nodeVerifyResults, verifyResult)
	}

	tc, err := TrustClusterPolicyEvaluate(attestInfo.Policies, nodeVerifyResults)
	if err != nil {
		return "", fmt.Errorf("trust cluster policy evaluate failed, error:%s", err)
	}

	claims, err := convertor.AnyToMap(tc)
	if err != nil {
		return "", fmt.Errorf("convert trust node cliams to map failed, error:%s", err)
	}

	finalToken, err := token.GenerateToken(claims)
	if err != nil {
		errMsg := fmt.Sprintf("generate token failed, error: %s", err)
		logs.Error(errMsg)
		return "", fmt.Errorf(errMsg)
	}

	logs.Debug("token is %s", finalToken)
	return finalToken, nil
}
