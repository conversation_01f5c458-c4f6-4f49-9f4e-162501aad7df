{"report": {"tee": "virtcca", "parameter": {"x5c": "MIIEjjCCA3agAwIBAgIREU42SbPDXIIcRtuLWsjAufUwDQYJKoZIhvcNAQELBQAwPTELMAkGA1UEBhMCQ04xDzANBgNVBAoTBkh1YXdlaTEdMBsGA1UEAxMUSHVhd2VpIElUIFByb2R1Y3QgQ0EwHhcNMjQwNTI4MDIxNDE1WhcNMzkwNTI1MDIxNDE1WjA2MQswCQYDVQQGEwJDTjEPMA0GA1UEChMGSHVhd2VpMRYwFAYDVQQDEw0xMDIzQTc5ODkxMzhUMIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEApmLUgbAe8uxQTAi0FNJPQZFW9j1wYZ/ezG2sdwmo+K3WZapOnJcAETnuIVEUdtRDg7UYF0L+W7/ZYtOVtXohvpv4FAoOxDAkifPTbcukRQ7NE3P0Gz39S5kkjcx0QR5LwM4AwWP/YaESGZkNwcln1HguiRCY7fj/07WVFGfAC+xwDT4rwmY23plxjfuJeKk4qgJf+0FbATmKoXQzC2/h+yK9qlrJzdQv02nEutIRufsA8b90Vbe7rVMUQy9IqOzxIG0WDNkoHcEo7sl3NugOeaSsMRiycSbax+onGeA/C2HTEmA6klQxbSPngIzvVMrGQsKRDDPaC5QoSAghIaAL1OvW/kBn76Mt6X8uTv8alwdOg1Dgjqdn01wpK+QT3tQN7oCP1g0tbqgQFFBsbpLhBw6dlmTNvIS7OPQJQ3eMgklo4vk0BH26r4Zn9c/1BkDnFtsnrWwSgUPX0FszAxzgzdraOxsbueNGIUs86rVAQ/0lnOVLP5ixSMrpSa5WymEgZ/pJXy02dXgv3LtYc9gGpQHl3kqoY0LnZLLhFSTu5AznJZLLkoNPTd8XGv+cgkBdqqTBWRNZ3jXHXHqwwAU9xwVdH6Z1hfOfFET437elytE8+jjN0ZCuIlb9usyzbaRo2qnMiHJHupMwfw52bvYCWPIyGUaRWgYpYLTkr2MF7A8CAwEAAaOBjzCBjDAfBgNVHSMEGDAWgBQSijfs+XNX1+SDurVvA+zdrhFOzzALBgNVHQ8EBAMCA/gwXAYIKwYBBQUHAQEEUDBOMCgGCCsGAQUFBzAChhxodHRwOi8vMTI3LjAuMC4xL2NhaXNzdWUuaHRtMCIGCCsGAQUFBzABhhZodHRwOi8vMTI3LjAuMC4xOjIwNDQzMA0GCSqGSIb3DQEBCwUAA4IBAQAsiyQZ7V9bOZDaFg9atpKWzV9B0gf1rtdTo2MNzFs1r63N2ZprUmQc649TJ3l5INcG0GHbdDqvvHpAVB41nlqXQPqAziHXp7v4YgQxXNnbDVt2mJIKcr2S6Ox9N59IqckSQouanmRmjbW7g90gsIS55rVH2QfuFgCpnEOf6MBEvWAU37omKFF6j56Cv/lMMNLsMzJgsE6dXwLEjDqykoB3ceantt/YSNZbltiF+zSlSBOdAD5T+uoquTP6cW4VIkmDJ8G9/pZaLJ4JycmwLtkuzjm3+c0vXPa88rOdpz3mO07BqsXYfSPXWMH208QxPwyQUbmyQwS5OSoeuRvGw22i"}, "tee_report": "2QGPoRms0VkFjdKERKEBOCSgWQN/WQN8pwpYQB+rVhlgPiYD0ZH5b9+p4XJyUA/3qrpkEnfnNkdJIaQxThkGQ2hlWTR7ITHLvl/rOiBjQND1JYDf6HKTZw/Jv0MZrMtYQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAZrM1ZAiYwggIiMA0GCSqGSIb3DQEBAQUAA4ICDwAwggIKAoICAQCmYtSBsB7y7FBMCLQU0k9BkVb2PXBhn97Mbax3Caj4rdZlqk6clwAROe4hURR21EODtRgXQv5bv9li05W1eiG+m/gUCg7EMCSJ89Nty6RFDs0Tc/QbPf1LmSSNzHRBHkvAzgDBY/9hoRIZmQ3ByWfUeC6JEJjt+P/TtZUUZ8AL7HANPivCZjbemXGN+4l4qTiqAl/7QVsBOYqhdDMLb+H7Ir2qWsnN1C/TacS60hG5+wDxv3RVt7utUxRDL0io7PEgbRYM2SgdwSjuyXc26A55pKwxGLJxJtrH6icZ4D8LYdMSYDqSVDFtI+eAjO9UysZCwpEMM9oLlChICCEhoAvU69b+QGfvoy3pfy5O/xqXB06DUOCOp2fTXCkr5BPe1A3ugI/WDS1uqBAUUGxukuEHDp2WZM28hLs49AlDd4yCSWji+TQEfbqvhmf1z/UGQOcW2yetbBKBQ9fQWzMDHODN2to7Gxu540YhSzzqtUBD/SWc5Us/mLFIyulJrlbKYSBn+klfLTZ1eC/cu1hz2AalAeXeSqhjQudksuEVJO7kDOclksuSg09N3xca/5yCQF2qpMFZE1neNcdcerDABT3HBV0fpnWF858URPjft6XK0Tz6OM3RkK4iVv26zLNtpGjaqcyIcke6kzB/DnZu9gJY8jIZRpFaBilgtOSvYwXsDwIDAQABGazMZ3NoYS0yNTYZrNBnc2hhLTI1Nhmszlgg6iHM4buDbKeXhKwRov4ojGceX2LCiDcY7Y2lmUeuKLEZrM+EWCA3pKBP5MshqAqBQZ6091kvcnEGlyf7XrhZ/RtQMZv65FggAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABYIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAWCAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFkCAE3UOgzQgssusjyzjdyaOElJKlqkhAfPQvO8CnD2fRuEEBhQzktkX6ad5rCwzlI3LrCmog7JFiGyexVw0ZWZkWttsF9wiGiE87AAnB3Bu7CduuE257ZvHhXNXMh6odgsS2SXDkB531ReomZp7lN0Rwg+LfYVBsVT5vSSCi8ixduL6DqH3CdEr9d30cDw5BiDYF0rj13q87U/+euosu3kmF0ROsMsOHkugEk+ztEvbfjKqEpKNRUvMsw52ntMRv+Zqe7Ib6DWYdiYAHHufrhAXPIX5oF8TNrZ5iK3Dq62Gmb0mbvd4FQ8l7RuCoJF/+SIM54lTAF/o30ww3Fch6m5zejK8Qyx0KDjAN+6C60j4Z0sAP3qINwMq5I5jYo0Gxf6wYItcpXeHE26xtoWatbW043ZryE6js/qwuoXmNc1xE2b3NACg6P/YB4uFsK2bmIqear0/uNkOO5Fxs2bi754hM2T/vWOuyvgma0IWdmRkrWt/kYz+vwMsx2vnSxopOivTn6yAEwWrSSa9X7fV3k+lcVrfCjCeDrr5MrSbd+GJp5GKbG74oSdY0xlJXJEZ7HM/UBOJ0A0K8VeShdyhK7zj2H6WMmm14UDUnMqK2iScjIO+NYD8ycZG7gzKRWObCbRgMj7w6eBpbWDXHwvaOB9DHapQN7PHqk5+jTFDH9JQo5b"}}